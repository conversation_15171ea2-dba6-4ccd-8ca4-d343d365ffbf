using CommandGuard.Models;
using FreeSql;

namespace CommandGuard.Data;

/// <summary>
/// 应用程序数据库上下文，提供对所有实体的数据访问
/// </summary>
public class AppDbContext
{
    #region 属性

    /// <summary>FreeSql实例，提供数据库操作接口</summary>
    private IFreeSql FreeSql { get; }

    #endregion

    #region 构造函数

    /// <summary>
    /// 构造函数，初始化数据库上下文
    /// </summary>
    /// <param name="freeSql">FreeSql实例</param>
    public AppDbContext(IFreeSql freeSql)
    {
        FreeSql = freeSql;

        // 初始化数据库结构，确保所有表都存在
        InitializeDatabase();
    }

    #endregion

    #region 实体操作接口

    // Member 实体操作
    /// <summary>成员查询接口</summary>
    public ISelect<Member> Members => FreeSql.Select<Member>();
    /// <summary>成员插入接口</summary>
    public IInsert<Member> InsertMember => FreeSql.Insert<Member>();
    /// <summary>成员更新接口</summary>
    public IUpdate<Member> UpdateMember => FreeSql.Update<Member>();
    /// <summary>成员删除接口</summary>
    public IDelete<Member> DeleteMember => FreeSql.Delete<Member>();

    // Message 实体操作
    /// <summary>消息查询接口</summary>
    public ISelect<Models.InternalMessage> Messages => FreeSql.Select<Models.InternalMessage>();
    /// <summary>消息插入接口</summary>
    public IInsert<Models.InternalMessage> InsertMessage => FreeSql.Insert<Models.InternalMessage>();
    /// <summary>消息更新接口</summary>
    public IUpdate<Models.InternalMessage> UpdateMessage => FreeSql.Update<Models.InternalMessage>();
    /// <summary>消息删除接口</summary>
    public IDelete<Models.InternalMessage> DeleteMessage => FreeSql.Delete<Models.InternalMessage>();

    // DepositWithdrawRequest 实体操作
    /// <summary>存取款申请查询接口</summary>
    public ISelect<DepositWithdrawRequest> DepositWithdrawRequests => FreeSql.Select<DepositWithdrawRequest>();
    /// <summary>存取款申请插入接口</summary>
    public IInsert<DepositWithdrawRequest> InsertDepositWithdrawRequest => FreeSql.Insert<DepositWithdrawRequest>();
    /// <summary>存取款申请更新接口</summary>
    public IUpdate<DepositWithdrawRequest> UpdateDepositWithdrawRequest => FreeSql.Update<DepositWithdrawRequest>();
    /// <summary>存取款申请删除接口</summary>
    public IDelete<DepositWithdrawRequest> DeleteDepositWithdrawRequest => FreeSql.Delete<DepositWithdrawRequest>();

    // Financial 实体操作
    /// <summary>财务记录查询接口</summary>
    public ISelect<Financial> Financials => FreeSql.Select<Financial>();
    /// <summary>财务记录插入接口</summary>
    public IInsert<Financial> InsertFinancial => FreeSql.Insert<Financial>();
    /// <summary>财务记录更新接口</summary>
    public IUpdate<Financial> UpdateFinancial => FreeSql.Update<Financial>();
    /// <summary>财务记录删除接口</summary>
    public IDelete<Financial> DeleteFinancial => FreeSql.Delete<Financial>();

    // BetOrder 实体操作
    /// <summary>投注订单查询接口</summary>
    public ISelect<BetOrder> BetOrders => FreeSql.Select<BetOrder>();
    /// <summary>投注订单插入接口</summary>
    public IInsert<BetOrder> InsertBetOrder => FreeSql.Insert<BetOrder>();
    /// <summary>投注订单更新接口</summary>
    public IUpdate<BetOrder> UpdateBetOrder => FreeSql.Update<BetOrder>();
    /// <summary>投注订单删除接口</summary>
    public IDelete<BetOrder> DeleteBetOrder => FreeSql.Delete<BetOrder>();

    // FlightOrder 实体操作
    /// <summary>飞单订单查询接口</summary>
    public ISelect<FlightOrder> FlightOrders => FreeSql.Select<FlightOrder>();
    /// <summary>飞单订单插入接口</summary>
    public IInsert<FlightOrder> InsertFlightOrder => FreeSql.Insert<FlightOrder>();
    /// <summary>飞单订单更新接口</summary>
    public IUpdate<FlightOrder> UpdateFlightOrder => FreeSql.Update<FlightOrder>();
    /// <summary>飞单订单删除接口</summary>
    public IDelete<FlightOrder> DeleteFlightOrder => FreeSql.Delete<FlightOrder>();

    #endregion

    #region 通用操作方法

    /// <summary>
    /// 获取指定实体类型的查询接口
    /// </summary>
    /// <typeparam name="T">实体类型</typeparam>
    /// <returns>查询接口</returns>
    public ISelect<T> Select<T>() where T : class => FreeSql.Select<T>();

    /// <summary>
    /// 获取指定实体类型的插入接口
    /// </summary>
    /// <typeparam name="T">实体类型</typeparam>
    /// <returns>插入接口</returns>
    public IInsert<T> Insert<T>() where T : class => FreeSql.Insert<T>();

    /// <summary>
    /// 获取指定实体类型的更新接口
    /// </summary>
    /// <typeparam name="T">实体类型</typeparam>
    /// <returns>更新接口</returns>
    public IUpdate<T> Update<T>() where T : class => FreeSql.Update<T>();

    /// <summary>
    /// 获取指定实体类型的删除接口
    /// </summary>
    /// <typeparam name="T">实体类型</typeparam>
    /// <returns>删除接口</returns>
    public IDelete<T> Delete<T>() where T : class => FreeSql.Delete<T>();

    #endregion

    #region 数据库初始化
    /// <summary>
    /// 初始化数据库结构
    /// </summary>
    private void InitializeDatabase()
    {
        // 同步所有实体的数据库结构，确保表存在且字段匹配
        SyncEntityStructure<Member>();
        SyncEntityStructure<Models.InternalMessage>();
        SyncEntityStructure<DepositWithdrawRequest>();
        SyncEntityStructure<Financial>();
        SyncEntityStructure<BetOrder>();
        SyncEntityStructure<FlightOrder>();

        // 未来添加新实体时，在这里添加同步调用
        // SyncEntityStructure<User>();
        // SyncEntityStructure<Product>();
    }

    /// <summary>
    /// 同步指定实体的数据库结构
    /// </summary>
    /// <typeparam name="T">实体类型</typeparam>
    private void SyncEntityStructure<T>() where T : class
    {
        try
        {
            // 使用FreeSql的CodeFirst功能自动创建或更新表结构
            FreeSql.CodeFirst.SyncStructure<T>();
        }
        catch (Exception ex)
        {
            // 数据库结构同步失败时抛出详细异常信息
            throw new InvalidOperationException(@$"同步实体 {typeof(T).Name} 的数据库结构失败", ex);
        }
    }
    #endregion
}
