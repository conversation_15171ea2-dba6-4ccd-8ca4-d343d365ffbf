using CommandGuard.Interfaces.Repositories;
using CommandGuard.Interfaces.Services;
using Microsoft.Extensions.Logging;

namespace CommandGuard.Services;

public class DepositWithdrawRequestService(
    ILogger<DepositWithdrawRequestService> logger,
    IDepositWithdrawRequestRepository requestRepository,
    IMemberRepository memberRepository,
    IMemberService memberService
) : IDepositWithdrawRequestService
{

}