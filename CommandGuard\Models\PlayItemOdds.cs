﻿using System.ComponentModel.DataAnnotations;
using CommandGuard.Enums;
using FreeSql.DataAnnotations;

namespace CommandGuard.Models;

[Table(Name = "PlayItemOdds")]
public class PlayItemOdds
{
    /// <summary>
    /// 主键ID，自增长
    /// </summary>
    [Column(IsIdentity = true, IsPrimary = true)]
    public int Id { get; set; }

    /// <summary>
    /// 投注彩种
    /// </summary>
    [Required(ErrorMessage = @"投注彩种不能为空")]
    [Column(IsNullable = false)]
    public EnumBetLottery BetLottery { get; set; }

    /// <summary>
    /// 投注项目名称
    /// </summary>
    [Required(ErrorMessage = @"投注项目名称不能为空")]
    [StringLength(20, ErrorMessage = @"投注项目名称长度不能超过20个字符")]
    [Column(StringLength = 20)]
    public string PlayItem { get; set; } = string.Empty;

    /// <summary>
    /// 赔率
    /// </summary>
    [Range(1.0, 100.0, ErrorMessage = @"赔率必须在1.0-100.0之间")]
    [Column(Precision = 8, Scale = 3)]
    public decimal Odds { get; set; }

    /// <summary>
    /// 最小投注金额
    /// </summary>
    [Range(0.01, double.MaxValue, ErrorMessage = @"最小投注金额必须大于0")]
    [Column(Precision = 18, Scale = 2)]
    public decimal MinStake { get; set; }

    /// <summary>
    /// 最大投注金额
    /// </summary>
    [Range(0.01, double.MaxValue, ErrorMessage = @"最大投注金额必须大于0")]
    [Column(Precision = 18, Scale = 2)]
    public decimal MaxStake { get; set; }

    /// <summary>
    /// 总投注限额
    /// </summary>
    [Range(0.01, double.MaxValue, ErrorMessage = @"总投注限额必须大于0")]
    [Column(Precision = 18, Scale = 2)]
    public decimal TotalStake { get; set; }
}