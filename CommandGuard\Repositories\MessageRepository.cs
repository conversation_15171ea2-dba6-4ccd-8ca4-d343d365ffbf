using CommandGuard.Data;
using CommandGuard.Interfaces.Repositories;
using Microsoft.Extensions.Logging;

namespace CommandGuard.Repositories;

/// <summary>
/// 消息仓储实现类
/// 提供消息相关的数据访问方法实现
/// </summary>
public class MessageRepository(AppDbContext dbContext, ILogger<MessageRepository> logger)
    : BaseRepository<Models.InternalMessage>(dbContext, logger), IMessageRepository
{
    /// <summary>
    /// 获取所有未读消息
    /// </summary>
    /// <returns>未读消息列表，按创建时间升序排列</returns>
    public async Task<IEnumerable<Models.InternalMessage>> GetUnreadMessagesAsync()
    {
        await Semaphore.WaitAsync();
        try
        {
            Logger.LogDebug(@"获取所有未读消息");

            var messages = await DbContext.Messages
                .Where(m => !m.IsRead)
                .OrderBy(m => m.CreatedAt)
                .ToListAsync();

            Logger.LogDebug(@"未读消息查询结果: 找到 {Count} 条未读消息", messages.Count);
            return messages;
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, @"获取未读消息失败");
            throw;
        }
        finally
        {
            Semaphore.Release();
        }
    }

    /// <summary>
    /// 根据账号获取消息列表
    /// </summary>
    /// <param name="account">发送者账号</param>
    /// <param name="includeRead">是否包含已读消息，默认为true</param>
    /// <returns>该账号的消息列表，按创建时间降序排列</returns>
    public async Task<IEnumerable<Models.InternalMessage>> GetMessagesByAccountAsync(string account, bool includeRead = true)
    {
        await Semaphore.WaitAsync();
        try
        {
            Logger.LogDebug(@"根据账号获取消息: {Account}, 包含已读: {IncludeRead}", account, includeRead);

            var query = DbContext.Messages.Where(m => m.Account == account);

            if (!includeRead)
            {
                query = query.Where(m => !m.IsRead);
            }

            var messages = await query
                .OrderByDescending(m => m.CreatedAt)
                .ToListAsync();

            Logger.LogDebug(@"账号消息查询结果: {Account} - 找到 {Count} 条消息", account, messages.Count);
            return messages;
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, @"根据账号获取消息失败: {Account}", account);
            throw;
        }
        finally
        {
            Semaphore.Release();
        }
    }

    /// <summary>
    /// 标记消息为已读
    /// </summary>
    /// <param name="messageId">消息ID</param>
    /// <returns>标记是否成功</returns>
    public async Task<bool> MarkAsReadAsync(long messageId)
    {
        await Semaphore.WaitAsync();
        try
        {
            Logger.LogDebug(@"标记消息为已读: {MessageId}", messageId);

            var affectedRows = await DbContext.UpdateMessage
                .Set(m => m.IsRead, true)
                .Set(m => m.UpdatedAt, DateTime.Now)
                .Where(m => m.Id == messageId)
                .ExecuteAffrowsAsync();

            var success = affectedRows > 0;
            Logger.LogDebug(@"消息标记结果: {MessageId} - {Success}", messageId, success);
            return success;
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, @"标记消息为已读失败: {MessageId}", messageId);
            throw;
        }
        finally
        {
            Semaphore.Release();
        }
    }

    /// <summary>
    /// 批量标记消息为已读
    /// </summary>
    /// <param name="messageIds">消息ID列表</param>
    /// <returns>成功标记的消息数量</returns>
    public async Task<int> BatchMarkAsReadAsync(IEnumerable<long> messageIds)
    {
        await Semaphore.WaitAsync();
        try
        {
            var idList = messageIds.ToList();
            Logger.LogInformation(@"批量标记消息为已读: {Count} 条消息", idList.Count);

            var affectedRows = await DbContext.UpdateMessage
                .Set(m => m.IsRead, true)
                .Set(m => m.UpdatedAt, DateTime.Now)
                .Where(m => idList.Contains(m.Id))
                .ExecuteAffrowsAsync();

            Logger.LogInformation(@"批量标记完成: 影响 {Rows} 行", affectedRows);
            return affectedRows;
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, @"批量标记消息为已读失败");
            throw;
        }
        finally
        {
            Semaphore.Release();
        }
    }
    /// <summary>
    /// 获取指定账号的未读消息数量
    /// </summary>
    /// <param name="account">发送者账号</param>
    /// <returns>未读消息数量</returns>
    public async Task<int> GetUnreadCountByAccountAsync(string account)
    {
        await Semaphore.WaitAsync();
        try
        {
            Logger.LogDebug(@"获取账号未读消息数量: {Account}", account);

            var count = await DbContext.Messages
                .Where(m => m.Account == account && !m.IsRead)
                .CountAsync();

            Logger.LogDebug(@"账号未读消息数量: {Account} - {Count}", account, count);
            return count;
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, @"获取账号未读消息数量失败: {Account}", account);
            throw;
        }
        finally
        {
            Semaphore.Release();
        }
    }

    /// <summary>
    /// 根据时间范围查询消息
    /// </summary>
    /// <param name="startTime">开始时间</param>
    /// <param name="endTime">结束时间</param>
    /// <param name="account">发送者账号，可选</param>
    /// <returns>指定时间范围内的消息列表</returns>
    public async Task<IEnumerable<Models.InternalMessage>> GetMessagesByTimeRangeAsync(DateTime startTime, DateTime endTime, string? account = null)
    {
        await Semaphore.WaitAsync();
        try
        {
            Logger.LogDebug(@"根据时间范围查询消息: {StartTime} - {EndTime}, 账号: {Account}", startTime, endTime, account ?? @"全部");

            var query = DbContext.Messages
                .Where(m => m.CreatedAt >= startTime && m.CreatedAt <= endTime);

            if (!string.IsNullOrEmpty(account))
            {
                query = query.Where(m => m.Account == account);
            }

            var messages = await query
                .OrderByDescending(m => m.CreatedAt)
                .ToListAsync();

            Logger.LogDebug(@"时间范围消息查询结果: 找到 {Count} 条消息", messages.Count);
            return messages;
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, @"根据时间范围查询消息失败");
            throw;
        }
        finally
        {
            Semaphore.Release();
        }
    }

    /// <summary>
    /// 搜索包含指定关键词的消息
    /// </summary>
    /// <param name="keyword">搜索关键词</param>
    /// <param name="account">发送者账号，可选</param>
    /// <returns>包含关键词的消息列表</returns>
    public async Task<IEnumerable<Models.InternalMessage>> SearchMessagesAsync(string keyword, string? account = null)
    {
        await Semaphore.WaitAsync();
        try
        {
            Logger.LogDebug(@"搜索消息关键词: {Keyword}, 账号: {Account}", keyword, account ?? @"全部");

            var query = DbContext.Messages
                .Where(m => m.Content.Contains(keyword));

            if (!string.IsNullOrEmpty(account))
            {
                query = query.Where(m => m.Account == account);
            }

            var messages = await query
                .OrderByDescending(m => m.CreatedAt)
                .ToListAsync();

            Logger.LogDebug(@"关键词搜索结果: {Keyword} - 找到 {Count} 条消息", keyword, messages.Count);
            return messages;
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, @"搜索消息失败: {Keyword}", keyword);
            throw;
        }
        finally
        {
            Semaphore.Release();
        }
    }

    /// <summary>
    /// 删除指定时间之前的已读消息
    /// </summary>
    /// <param name="beforeTime">时间界限</param>
    /// <returns>删除的消息数量</returns>
    public async Task<int> DeleteReadMessagesBeforeAsync(DateTime beforeTime)
    {
        await Semaphore.WaitAsync();
        try
        {
            Logger.LogInformation(@"删除指定时间之前的已读消息: {BeforeTime}", beforeTime);

            var affectedRows = await DbContext.DeleteMessage
                .Where(m => m.IsRead && m.CreatedAt < beforeTime)
                .ExecuteAffrowsAsync();

            Logger.LogInformation(@"删除已读消息完成: 删除 {Rows} 条消息", affectedRows);
            return affectedRows;
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, @"删除已读消息失败: {BeforeTime}", beforeTime);
            throw;
        }
        finally
        {
            Semaphore.Release();
        }
    }

    /// <summary>
    /// 获取消息统计信息
    /// </summary>
    /// <returns>包含总消息数、未读消息数等统计信息</returns>
    public async Task<(int TotalMessages, int UnreadMessages, int TodayMessages)> GetMessageStatisticsAsync()
    {
        await Semaphore.WaitAsync();
        try
        {
            Logger.LogDebug(@"获取消息统计信息");

            var today = DateTime.Today;
            var tomorrow = today.AddDays(1);

            var totalMessages = await DbContext.Messages.CountAsync();
            var unreadMessages = await DbContext.Messages.Where(m => !m.IsRead).CountAsync();
            var todayMessages = await DbContext.Messages.Where(m => m.CreatedAt >= today && m.CreatedAt < tomorrow).CountAsync();

            var result = (totalMessages, unreadMessages, todayMessages);
            Logger.LogDebug(@"消息统计结果: 总消息={TotalMessages}, 未读消息={UnreadMessages}, 今日消息={TodayMessages}",
                result.totalMessages, result.unreadMessages, result.todayMessages);
            return result;
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, @"获取消息统计信息失败");
            throw;
        }
        finally
        {
            Semaphore.Release();
        }
    }
}