namespace CommandGuard.Interfaces.Services;

/// <summary>
/// HTTP API服务接口
/// 定义HTTP API服务的核心功能，包括启动、停止和生命周期管理
/// 基于HttpListener实现轻量级HTTP服务器功能
/// </summary>
public interface IHttpApiService : IDisposable
{
    /// <summary>
    /// 启动HTTP API服务
    /// </summary>
    /// <param name="port">监听端口，默认5000</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>启动任务</returns>
    Task StartAsync(int port = 5000, CancellationToken cancellationToken = default);

    /// <summary>
    /// 停止HTTP API服务
    /// </summary>
    /// <returns>停止任务</returns>
    Task StopAsync();
}
