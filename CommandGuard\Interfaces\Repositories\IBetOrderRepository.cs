using CommandGuard.Models;
using CommandGuard.Enums;

namespace CommandGuard.Interfaces.Repositories;

/// <summary>
/// 投注订单仓储接口
/// 提供投注订单相关的数据访问方法，包括基础CRUD操作和业务特定查询
/// </summary>
public interface IBetOrderRepository : IRepository<BetOrder>
{
    /// <summary>
    /// 根据订单号查询投注订单
    /// </summary>
    /// <param name="orderNumber">订单号</param>
    /// <returns>投注订单信息，如果不存在则返回null</returns>
    Task<BetOrder?> GetByOrderNumberAsync(string orderNumber);

    /// <summary>
    /// 根据账号查询投注订单列表
    /// </summary>
    /// <param name="account">投注用户账号</param>
    /// <param name="pageIndex">页码，从1开始</param>
    /// <param name="pageSize">每页数量</param>
    /// <returns>该账号的投注订单列表</returns>
    Task<(IEnumerable<BetOrder> Orders, int TotalCount)> GetByAccountAsync(string account, int pageIndex = 1, int pageSize = 20);

    /// <summary>
    /// 根据状态查询投注订单
    /// </summary>
    /// <param name="status">订单状态</param>
    /// <param name="pageIndex">页码，从1开始</param>
    /// <param name="pageSize">每页数量</param>
    /// <returns>指定状态的投注订单列表</returns>
    Task<(IEnumerable<BetOrder> Orders, int TotalCount)> GetByStatusAsync(EnumBetOrderStatus status, int pageIndex = 1, int pageSize = 20);

    /// <summary>
    /// 根据期号查询投注订单
    /// </summary>
    /// <param name="issue">投注期号</param>
    /// <returns>该期号的所有投注订单</returns>
    Task<IEnumerable<BetOrder>> GetByIssueAsync(string issue);

    /// <summary>
    /// 根据彩种查询投注订单
    /// </summary>
    /// <param name="betLottery">投注彩种</param>
    /// <param name="pageIndex">页码，从1开始</param>
    /// <param name="pageSize">每页数量</param>
    /// <returns>指定彩种的投注订单列表</returns>
    Task<(IEnumerable<BetOrder> Orders, int TotalCount)> GetByLotteryAsync(EnumBetLottery betLottery, int pageIndex = 1, int pageSize = 20);

    /// <summary>
    /// 根据时间范围查询投注订单
    /// </summary>
    /// <param name="startTime">开始时间</param>
    /// <param name="endTime">结束时间</param>
    /// <param name="account">投注用户账号，可选</param>
    /// <returns>指定时间范围内的投注订单</returns>
    Task<IEnumerable<BetOrder>> GetByTimeRangeAsync(DateTime startTime, DateTime endTime, string? account = null);

    /// <summary>
    /// 获取待结算的投注订单
    /// </summary>
    /// <param name="issue">期号，可选</param>
    /// <returns>待结算的投注订单列表</returns>
    Task<IEnumerable<BetOrder>> GetPendingSettlementOrdersAsync(string? issue = null);

    /// <summary>
    /// 批量更新订单状态
    /// </summary>
    /// <param name="orderIds">订单ID列表</param>
    /// <param name="newStatus">新状态</param>
    /// <returns>更新的订单数量</returns>
    Task<int> BatchUpdateStatusAsync(IEnumerable<long> orderIds, EnumBetOrderStatus newStatus);

    /// <summary>
    /// 获取投注统计信息
    /// </summary>
    /// <param name="account">账号，可选</param>
    /// <param name="startTime">开始时间，可选</param>
    /// <param name="endTime">结束时间，可选</param>
    /// <returns>投注统计信息</returns>
    Task<(int TotalOrders, decimal TotalAmount, decimal TotalWinAmount, decimal TotalRebateAmount)> GetBetStatisticsAsync(string? account = null, DateTime? startTime = null, DateTime? endTime = null);

    /// <summary>
    /// 检查订单号是否已存在
    /// </summary>
    /// <param name="orderNumber">订单号</param>
    /// <returns>如果订单号已存在返回true，否则返回false</returns>
    Task<bool> IsOrderNumberExistsAsync(string orderNumber);
}
