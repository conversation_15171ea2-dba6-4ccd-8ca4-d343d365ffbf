namespace CommandGuard.Interfaces.Services;

/// <summary>
/// 机器人服务接口
/// 负责自动处理未读消息，提供持续的后台消息处理能力
/// 集成到项目的统一生命周期管理体系中
/// </summary>
public interface IRobotService : IDisposable
{
    /// <summary>
    /// 启动机器人服务
    /// 开始后台循环处理未读消息
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>启动任务</returns>
    Task StartAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// 停止机器人服务
    /// 停止后台消息处理循环
    /// </summary>
    /// <returns>停止任务</returns>
    Task StopAsync();
}
