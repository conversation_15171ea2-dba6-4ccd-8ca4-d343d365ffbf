namespace CommandGuard.Forms;

/// <summary>
/// FormMain 窗体设计器文件
/// 包含窗体的可视化设计和控件初始化代码
/// </summary>
partial class FormMain
{
    /// <summary>
    /// 必需的设计器变量
    /// </summary>
    private System.ComponentModel.IContainer components = null;

    /// <summary>
    /// 清理所有正在使用的资源
    /// </summary>
    /// <param name="disposing">如果应释放托管资源，为 true；否则为 false</param>
    protected override void Dispose(bool disposing)
    {
        if (disposing && (components != null))
        {
            components.Dispose();
        }
        base.Dispose(disposing);
    }

    #region Windows 窗体设计器生成的代码

    /// <summary>
    /// Required method for Designer support - do not modify
    /// the contents of this method with the code editor.
    /// </summary>
    private void InitializeComponent()
    {
        SuspendLayout();
        // 
        // FormMain
        // 
        AutoScaleDimensions = new System.Drawing.SizeF(7F, 17F);
        AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
        ClientSize = new System.Drawing.Size(800, 600);
        Text = "CommandGuard 管理系统";
        Load += FormMain_Load;
        ResumeLayout(false);
    }

    #endregion
}