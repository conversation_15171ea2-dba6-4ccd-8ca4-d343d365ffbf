using CommandGuard.Configuration;
using CommandGuard.Interfaces.Services;
using Microsoft.Extensions.Logging;

namespace CommandGuard.Services;

public class RobotService : IRobotService
{
    #region 私有字段

    private readonly ILogger<RobotService> _logger;
    private readonly IMessageService _messageService;
    private Task? _processingTask;
    private bool _disposed;

    // 配置参数
    private readonly TimeSpan _checkInterval = TimeSpan.FromSeconds(1); // 检查间隔：1秒
    private readonly TimeSpan _errorRetryInterval = TimeSpan.FromSeconds(5); // 错误重试间隔：5秒

    #endregion

    #region 构造函数

    /// <summary>
    /// 初始化机器人服务
    /// </summary>
    /// <param name="logger">日志记录器</param>
    /// <param name="messageService">消息服务</param>
    public RobotService(
        ILogger<RobotService> logger,
        IMessageService messageService)
    {
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        _messageService = messageService ?? throw new ArgumentNullException(nameof(messageService));
    }

    #endregion

    #region 公共方法

    /// <summary>
    /// 启动机器人服务
    /// 开始后台循环处理未读消息
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>启动任务</returns>
    public async Task StartAsync(CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation(@"正在启动机器人服务");

            // 使用RuntimeConfiguration中的统一取消令牌
            var robotCancellationToken = RuntimeConfiguration.RobotServiceCancellation?.Token ?? CancellationToken.None;

            // 启动后台消息处理循环
            _processingTask = ProcessMessagesAsync(robotCancellationToken);

            _logger.LogInformation(@"机器人服务启动成功，开始监控未读消息");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, @"启动机器人服务时发生错误");
            await Task.Delay(0, cancellationToken);
            throw;
        }
    }

    /// <summary>
    /// 停止机器人服务
    /// 停止后台消息处理循环
    /// </summary>
    /// <returns>停止任务</returns>
    public async Task StopAsync()
    {
        try
        {
            _logger.LogInformation(@"正在停止机器人服务");

            // 使用RuntimeConfiguration中的统一取消令牌进行取消
            RuntimeConfiguration.RobotServiceCancellation?.CancelAsync();

            if (_processingTask != null)
            {
                await _processingTask;
            }

            _logger.LogInformation(@"机器人服务已停止");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, @"停止机器人服务时发生错误");
        }
    }

    #endregion

    #region 私有方法 - 消息处理

    /// <summary>
    /// 消息处理主循环
    /// 持续监控和处理未读消息
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>处理任务</returns>
    private async Task ProcessMessagesAsync(CancellationToken cancellationToken)
    {
        _logger.LogInformation(@"机器人消息处理循环已启动");

        while (!cancellationToken.IsCancellationRequested)
        {
            try
            {
                // 先延时
                await Task.Delay(_checkInterval, cancellationToken);

                // 获取未读消息
                var unreadMessages = await _messageService.GetUnreadMessagesAsync();
                var messageList = unreadMessages.ToList();

                // 如果没有未读消息，继续下一轮
                if (!messageList.Any())
                {
                    continue;
                }

                // 有未读消息，开始处理
                _logger.LogInformation(@"发现 {Count} 条未读消息，开始处理", messageList.Count);

                // 遍历处理每条消息
                foreach (var message in messageList)
                {
                    // 如果取消请求已发出，退出循环
                    if (cancellationToken.IsCancellationRequested)
                    {
                        break;
                    }

                    // 处理单条消息
                    await ProcessSingleMessageAsync(message, cancellationToken);
                }

                _logger.LogInformation(@"完成处理 {Count} 条消息", messageList.Count);
            }
            catch (OperationCanceledException)
            {
                // 正常取消，退出循环
                _logger.LogInformation(@"机器人服务收到取消请求，正在退出处理循环");
                break;
            }
            catch (Exception ex)
            {
                // 记录异常，继续运行
                _logger.LogError(ex, @"处理消息时发生错误，将在 {Seconds} 秒后重试", _errorRetryInterval.TotalSeconds);

                try
                {
                    await Task.Delay(_errorRetryInterval, cancellationToken);
                }
                catch (OperationCanceledException)
                {
                    // 在等待重试期间被取消
                    break;
                }
            }
        }

        _logger.LogInformation(@"机器人消息处理循环已退出");
    }

    /// <summary>
    /// 处理单条消息
    /// </summary>
    /// <param name="internalMessage">要处理的消息</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>处理任务</returns>
    private async Task ProcessSingleMessageAsync(CommandGuard.Models.InternalMessage internalMessage, CancellationToken cancellationToken)
    {
        try
        {
            _logger.LogDebug(@"开始处理消息 ID: {MessageId}, 发送者: {Account}, 内容: {Content}",
                internalMessage.Id, internalMessage.Account, internalMessage.Content);

            // TODO: 在这里添加具体的消息处理逻辑
            // 当前使用占位符逻辑
            await ProcessMessagePlaceholderAsync(internalMessage, cancellationToken);

            // 更新消息为已读状态
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, @"处理消息时发生错误 ID: {MessageId}", internalMessage.Id);
            // 不重新抛出异常，继续处理下一条消息
        }
    }

    /// <summary>
    /// 消息处理占位符方法
    /// 后续将替换为具体的业务逻辑
    /// </summary>
    /// <param name="internalMessage">要处理的消息</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>处理任务</returns>
    private async Task ProcessMessagePlaceholderAsync(CommandGuard.Models.InternalMessage internalMessage, CancellationToken cancellationToken)
    {
        // 占位符：模拟消息处理时间
        await Task.Delay(100, cancellationToken);

        // 占位符：根据消息内容进行不同的处理
        if (internalMessage.Content.Contains("测试"))
        {
            _logger.LogInformation(@"[占位符] 检测到测试消息，执行测试处理逻辑");
        }
        else if (internalMessage.Content.Contains("系统"))
        {
            _logger.LogInformation(@"[占位符] 检测到系统消息，执行系统处理逻辑");
        }
        else
        {
            _logger.LogInformation(@"[占位符] 执行默认消息处理逻辑");
        }

        // 占位符：记录处理结果
        _logger.LogDebug(@"[占位符] 消息处理完成，消息ID: {MessageId}, 处理时间: {ProcessTime}",
            internalMessage.Id, DateTime.Now);
    }

    #endregion

    #region IDisposable实现

    /// <summary>
    /// 释放资源
    /// </summary>
    public void Dispose()
    {
        if (!_disposed)
        {
            // 使用RuntimeConfiguration中的统一取消令牌进行取消
            RuntimeConfiguration.RobotServiceCancellation?.Cancel();
            _disposed = true;
        }

        GC.SuppressFinalize(this);
    }

    #endregion
}