namespace CommandGuard.Interfaces.Repositories;

/// <summary>
/// 消息仓储接口
/// 提供消息相关的数据访问方法，包括基础CRUD操作和业务特定查询
/// </summary>
public interface IMessageRepository : IRepository<Models.InternalMessage>
{
    /// <summary>
    /// 获取所有未读消息
    /// </summary>
    /// <returns>未读消息列表，按创建时间升序排列</returns>
    Task<IEnumerable<Models.InternalMessage>> GetUnreadMessagesAsync();

    /// <summary>
    /// 根据账号获取消息列表
    /// </summary>
    /// <param name="account">发送者账号</param>
    /// <param name="includeRead">是否包含已读消息，默认为true</param>
    /// <returns>该账号的消息列表，按创建时间降序排列</returns>
    Task<IEnumerable<Models.InternalMessage>> GetMessagesByAccountAsync(string account, bool includeRead = true);

    /// <summary>
    /// 标记消息为已读
    /// </summary>
    /// <param name="messageId">消息ID</param>
    /// <returns>标记是否成功</returns>
    Task<bool> MarkAsReadAsync(long messageId);

    /// <summary>
    /// 批量标记消息为已读
    /// </summary>
    /// <param name="messageIds">消息ID列表</param>
    /// <returns>成功标记的消息数量</returns>
    Task<int> BatchMarkAsReadAsync(IEnumerable<long> messageIds);

    /// <summary>
    /// 获取指定账号的未读消息数量
    /// </summary>
    /// <param name="account">发送者账号</param>
    /// <returns>未读消息数量</returns>
    Task<int> GetUnreadCountByAccountAsync(string account);

    /// <summary>
    /// 根据时间范围查询消息
    /// </summary>
    /// <param name="startTime">开始时间</param>
    /// <param name="endTime">结束时间</param>
    /// <param name="account">发送者账号，可选</param>
    /// <returns>指定时间范围内的消息列表</returns>
    Task<IEnumerable<Models.InternalMessage>> GetMessagesByTimeRangeAsync(DateTime startTime, DateTime endTime, string? account = null);

    /// <summary>
    /// 搜索包含指定关键词的消息
    /// </summary>
    /// <param name="keyword">搜索关键词</param>
    /// <param name="account">发送者账号，可选</param>
    /// <returns>包含关键词的消息列表</returns>
    Task<IEnumerable<Models.InternalMessage>> SearchMessagesAsync(string keyword, string? account = null);

    /// <summary>
    /// 删除指定时间之前的已读消息
    /// </summary>
    /// <param name="beforeTime">时间界限</param>
    /// <returns>删除的消息数量</returns>
    Task<int> DeleteReadMessagesBeforeAsync(DateTime beforeTime);

    /// <summary>
    /// 获取消息统计信息
    /// </summary>
    /// <returns>包含总消息数、未读消息数等统计信息</returns>
    Task<(int TotalMessages, int UnreadMessages, int TodayMessages)> GetMessageStatisticsAsync();
}
