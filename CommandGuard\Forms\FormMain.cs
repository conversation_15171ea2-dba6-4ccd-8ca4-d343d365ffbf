using Microsoft.Extensions.Logging;

namespace CommandGuard.Forms;

/// <summary>
/// 主窗体类，提供应用程序的主界面
/// 使用依赖注入获取服务层，实现UI与业务逻辑分离
/// </summary>
public partial class FormMain : Form
{
    #region 字段

    /// <summary>日志记录器</summary>
    private readonly ILogger<FormMain> _logger;

    #endregion

    #region 构造函数和初始化

    /// <summary>
    /// 构造函数，通过依赖注入初始化服务
    /// </summary>
    /// <param name="logger">日志记录器</param>
    public FormMain(ILogger<FormMain> logger)
    {
        _logger = logger;
        InitializeComponent();
    }

    /// <summary>
    /// 窗体加载事件处理
    /// </summary>
    /// <param name="sender">事件发送者</param>
    /// <param name="e">事件参数</param>
    private void FormMain_Load(object sender, EventArgs e)
    {
        try
        {
            _logger.LogInformation(@"主窗体加载");
            // TODO: 在这里添加窗体加载时的初始化逻辑
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, @"主窗体加载失败");
            MessageBox.Show(@$"加载失败: {ex.Message}", @"错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
        }
    }

    #endregion
}