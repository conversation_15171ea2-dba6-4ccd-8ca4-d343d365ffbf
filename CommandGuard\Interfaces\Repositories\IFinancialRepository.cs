using CommandGuard.Models;
using CommandGuard.Enums;

namespace CommandGuard.Interfaces.Repositories;

/// <summary>
/// 财务记录仓储接口
/// 提供财务记录相关的数据访问方法，包括基础CRUD操作和业务特定查询
/// </summary>
public interface IFinancialRepository : IRepository<Financial>
{
    /// <summary>
    /// 根据账号查询财务记录
    /// </summary>
    /// <param name="account">成员账号</param>
    /// <param name="pageIndex">页码，从1开始</param>
    /// <param name="pageSize">每页数量</param>
    /// <returns>该账号的财务记录列表</returns>
    Task<(IEnumerable<Financial> Records, int TotalCount)> GetByAccountAsync(string account, int pageIndex = 1, int pageSize = 20);

    /// <summary>
    /// 根据财务类型查询记录
    /// </summary>
    /// <param name="type">财务类型</param>
    /// <param name="pageIndex">页码，从1开始</param>
    /// <param name="pageSize">每页数量</param>
    /// <returns>指定类型的财务记录列表</returns>
    Task<(IEnumerable<Financial> Records, int TotalCount)> GetByTypeAsync(EnumFinancialType type, int pageIndex = 1, int pageSize = 20);

    /// <summary>
    /// 根据时间范围查询财务记录
    /// </summary>
    /// <param name="startTime">开始时间</param>
    /// <param name="endTime">结束时间</param>
    /// <param name="account">成员账号，可选</param>
    /// <returns>指定时间范围内的财务记录</returns>
    Task<IEnumerable<Financial>> GetByTimeRangeAsync(DateTime startTime, DateTime endTime, string? account = null);

    /// <summary>
    /// 根据关联业务ID查询财务记录
    /// </summary>
    /// <param name="referenceId">关联业务ID</param>
    /// <returns>关联的财务记录列表</returns>
    Task<IEnumerable<Financial>> GetByReferenceIdAsync(long referenceId);

    /// <summary>
    /// 计算账号的当前余额
    /// </summary>
    /// <param name="account">成员账号</param>
    /// <returns>当前计算出的余额</returns>
    Task<decimal> CalculateCurrentBalanceAsync(string account);

    /// <summary>
    /// 获取账号的最后一条财务记录
    /// </summary>
    /// <param name="account">成员账号</param>
    /// <returns>最后一条财务记录，如果不存在则返回null</returns>
    Task<Financial?> GetLastRecordByAccountAsync(string account);

    /// <summary>
    /// 验证余额一致性
    /// </summary>
    /// <param name="account">成员账号</param>
    /// <returns>余额是否一致</returns>
    Task<bool> ValidateBalanceConsistencyAsync(string account);

    /// <summary>
    /// 获取财务统计信息
    /// </summary>
    /// <param name="account">账号，可选</param>
    /// <param name="startTime">开始时间，可选</param>
    /// <param name="endTime">结束时间，可选</param>
    /// <returns>财务统计信息</returns>
    Task<(decimal TotalIncome, decimal TotalExpense, decimal NetAmount, int TransactionCount)> GetFinancialStatisticsAsync(string? account = null, DateTime? startTime = null, DateTime? endTime = null);

    /// <summary>
    /// 获取各类型财务记录的汇总
    /// </summary>
    /// <param name="account">账号，可选</param>
    /// <param name="startTime">开始时间，可选</param>
    /// <param name="endTime">结束时间，可选</param>
    /// <returns>按类型分组的财务汇总</returns>
    Task<Dictionary<EnumFinancialType, (decimal TotalAmount, int Count)>> GetFinancialSummaryByTypeAsync(string? account = null, DateTime? startTime = null, DateTime? endTime = null);

    /// <summary>
    /// 检查是否存在余额异常的记录
    /// </summary>
    /// <returns>存在异常的账号列表</returns>
    Task<IEnumerable<string>> GetAccountsWithBalanceAnomaliesAsync();
}

