﻿using System.ComponentModel.DataAnnotations;
using CommandGuard.Enums;
using FreeSql.DataAnnotations;

namespace CommandGuard.Models;

[Table(Name = "BetOrders")]
public class BetOrder
{
    #region 基本信息

    /// <summary>
    /// 投注订单唯一标识符
    /// 数据库主键，自动递增，用于唯一标识每个投注订单记录
    /// 数据类型：long - 64位整数，支持海量投注订单
    /// 用途：订单标识、关联查询、业务追踪等
    /// </summary>
    [Column(IsIdentity = true, IsPrimary = true)]
    public long Id { get; set; }

    /// <summary>
    /// 投注订单号
    /// 业务层面的唯一订单标识，用于对外展示和查询
    /// 约束：固定9位长度，不能为空，系统内必须唯一
    /// 格式：通常为数字组合，如：123456789
    /// 用途：订单查询、客户服务、报表统计、对账等
    /// </summary>
    [Required(ErrorMessage = @"订单号不能为空")]
    [StringLength(9, MinimumLength = 9, ErrorMessage = @"订单号长度必须为9位")]
    [Column(StringLength = 9, IsNullable = false)]
    public string OrderNumber { get; set; } = string.Empty;

    /// <summary>
    /// 关联投注用户账号
    /// 进行投注的成员账号，必须是系统中存在的有效成员账号
    /// 约束：最大长度100字符，不能为空，必须关联有效的成员账号
    /// 用途：投注归属、余额扣减、盈亏计算、统计分析等
    /// 关联：与Member表的Account字段关联
    /// </summary>
    [Required(ErrorMessage = @"投注用户账号不能为空")]
    [StringLength(100, ErrorMessage = @"投注用户账号长度不能超过100个字符")]
    [Column(StringLength = 100, IsNullable = false)]
    public string Account { get; set; } = string.Empty;

    #endregion

    #region 答题信息

    /// <summary>
    /// 投注彩种
    /// </summary>
    [Required(ErrorMessage = @"投注彩种不能为空")]
    [Column(IsNullable = false)]
    public EnumBetLottery BetLottery { get; set; } 
    
    /// <summary>
    /// 投注期号
    /// </summary>
    [Required(ErrorMessage = @"投注期号不能为空")]
    [StringLength(50, ErrorMessage = @"投注期号长度不能超过50个字符")]
    [Column(StringLength = 50, IsNullable = false)]
    public string Issue { get; set; } = string.Empty;
    
    /// <summary>
    /// 投注玩法项目
    /// </summary>
    [Required(ErrorMessage = @"投注玩法不能为空")]
    [StringLength(20, ErrorMessage = @"投注玩法长度不能超过20个字符")]
    [Column(StringLength = 20, IsNullable = false)]
    public string PlayItem { get; set; } = string.Empty;

    /// <summary>
    /// 投注时的赔率
    /// </summary>
    [Required(ErrorMessage = @"投注赔率不能为空")]
    [Range(1.0, 100.0, ErrorMessage = @"赔率必须在1.0-100.0之间")]
    [Column(Precision = 8, Scale = 3, IsNullable = false)]
    public decimal Odds { get; set; }

    #endregion

    #region 金额信息

    /// <summary>
    /// 投注金额
    /// </summary>
    [Required(ErrorMessage = @"投注金额不能为空")]
    [Range(1, 9999999999999999.99, ErrorMessage = @"投注金额必须大于等于1")]
    [Column(Precision = 18, Scale = 2, IsNullable = false)]
    public decimal Amount { get; set; }

    /// <summary>
    /// 结算金额
    /// </summary>
    [Column(Precision = 18, Scale = 2, IsNullable = true)]
    public decimal? SettledAmount { get; set; }

    /// <summary>
    /// 回水金额
    /// </summary>
    [Column(Precision = 18, Scale = 2, IsNullable = false)]
    public decimal RebateAmount { get; set; }

    #endregion

    #region 状态和结果

    /// <summary>
    /// 订单状态
    /// </summary>
    [Required(ErrorMessage = @"订单状态不能为空")]
    [Column(IsNullable = false)]
    public EnumBetOrderStatus Status { get; set; } = EnumBetOrderStatus.Confirmed;

    /// <summary>
    /// 开奖结果
    /// </summary>
    [StringLength(1, ErrorMessage = @"开奖结果长度必须为1位")]
    [Column(StringLength = 1, IsNullable = true)]
    public string DrawResult { get; set; } = string.Empty;

    #endregion

    #region 关联信息

    /// <summary>
    /// 订单真假状态
    /// </summary>
    [Required(ErrorMessage = @"订单真假状态不能为空")]
    [Column(IsNullable = false)]
    public bool IsRealOrder { get; set; } = true;

    /// <summary>
    /// 关联消息ID
    /// </summary>
    [StringLength(50, ErrorMessage = @"关联消息ID长度不能超过50个字符")]
    [Column(StringLength = 50, IsNullable = true)]
    public string MessageId { get; set; } = string.Empty;

    #endregion

    #region 时间信息

    /// <summary>
    /// 订单创建时间
    /// </summary>
    [Required(ErrorMessage = @"创建时间不能为空")]
    [Column(IsNullable = false, ServerTime = DateTimeKind.Local)]
    public DateTime CreatedAt { get; set; } = DateTime.Now;

    /// <summary>
    /// 订单更新时间
    /// </summary>
    [Required(ErrorMessage = @"创建时间不能为空")]
    [Column(IsNullable = false, ServerTime = DateTimeKind.Local)]
    public DateTime UpdatedAt { get; set; } = DateTime.Now;
    
    /// <summary>
    /// 订单结算时间
    /// </summary>
    [Column(IsNullable = true)]
    public DateTime? SettledTime { get; set; }

    /// <summary>
    /// 回水发放时间
    /// </summary>
    [Column(IsNullable = true)]
    public DateTime? RebateTime { get; set; }

    #endregion
}