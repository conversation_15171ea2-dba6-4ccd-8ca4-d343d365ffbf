﻿using System.ComponentModel.DataAnnotations;
using CommandGuard.Enums;
using FreeSql.DataAnnotations;

namespace CommandGuard.Models;

[Table(Name = "FlightOrders")]
public class FlightOrder
{
    /// <summary>
    /// 飞单订单唯一标识符
    /// 数据库主键，自动递增，用于唯一标识每个飞单记录
    /// 数据类型：long - 64位整数，支持海量飞单记录
    /// 用途：飞单标识、关联查询、业务追踪等
    /// </summary>
    [Column(IsIdentity = true, IsPrimary = true)]
    public long Id { get; set; }

    /// <summary>
    /// 投注彩种
    /// </summary>
    [Required(ErrorMessage = @"投注彩种不能为空")]
    [Column(IsNullable = false)]
    public EnumBetLottery BetLottery { get; set; } 
    
    /// <summary>
    /// 投注期号
    /// 标识投注所属的游戏期次，用于区分不同轮次的投注
    /// 约束：最大长度50字符，不能为空
    /// 格式：通常为日期+期次，如：20241226001
    /// 用途：期次管理、数据分组、统计分析等
    /// </summary>
    [Required(ErrorMessage = @"投注期号不能为空")]
    [StringLength(50, ErrorMessage = @"投注期号长度不能超过50个字符")]
    [Column(StringLength = 50, IsNullable = false)]
    public string Issue { get; set; } = string.Empty;

    /// <summary>
    /// 投注玩法项目
    /// 标识具体的投注玩法类型，如：1正、2番、12角、大小、单双等
    /// 约束：最大长度20字符，不能为空
    /// 示例：1正、2番、12角、大、小、单、双、围骰等
    /// 用途：玩法识别、数据分类、统计分析等
    /// </summary>
    [Required(ErrorMessage = @"投注玩法不能为空")]
    [StringLength(20, ErrorMessage = @"投注玩法长度不能超过20个字符")]
    [Column(StringLength = 20, IsNullable = false)]
    public string PlayItem { get; set; } = string.Empty;

    /// <summary>
    /// 飞单金额
    /// 抛售到第三方平台的投注金额
    /// 数据类型：decimal(18,2) - 精度18位，小数点后2位
    /// 约束：必须大于等于1，不能为空
    /// 范围：1.00 到 9999999999999999.99
    /// 用途：资金管理、平台结算、统计分析等
    /// </summary>
    [Required(ErrorMessage = @"飞单金额不能为空")]
    [Range(1.00, 9999999999999999.99, ErrorMessage = @"飞单金额必须大于等于1.00")]
    [Column(Precision = 18, Scale = 2, IsNullable = false)]
    public decimal Amount { get; set; }

    /// <summary>
    /// 飞单状态
    /// 标识飞单当前所处的生命周期状态
    /// 枚举类型：EnumFlightOrderStatus
    /// 约束：不能为空，默认值为待飞单
    /// 状态流转：待飞单 → 飞单中 → 成功/失败
    /// 用途：状态管理、流程控制、业务追踪等
    /// </summary>
    [Required(ErrorMessage = @"飞单状态不能为空")]
    [Column(IsNullable = false)]
    public EnumFlightOrderStatus Status { get; set; } = EnumFlightOrderStatus.Pending;

    /// <summary>
    /// 飞单创建时间
    /// 飞单记录创建的时间戳，用于时间排序和统计分析
    /// 约束：不可为空，自动设置为当前时间，创建后不可修改
    /// 用途：时间排序、统计分析、数据归档、业务追踪等
    /// </summary>
    [Required(ErrorMessage = @"创建时间不能为空")]
    [Column(IsNullable = false, ServerTime = DateTimeKind.Local)]
    public DateTime CreatedAt { get; set; } = DateTime.Now;
}