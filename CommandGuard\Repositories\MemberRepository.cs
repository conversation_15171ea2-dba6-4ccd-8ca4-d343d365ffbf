using CommandGuard.Data;
using CommandGuard.Interfaces.Repositories;
using CommandGuard.Models;
using Microsoft.Extensions.Logging;

namespace CommandGuard.Repositories;

/// <summary>
/// 成员仓储实现类
/// 提供成员相关的数据访问方法实现
/// </summary>
public class MemberRepository(AppDbContext dbContext, ILogger<MemberRepository> logger)
    : BaseRepository<Member>(dbContext, logger), IMemberRepository
{
    /// <summary>
    /// 根据账号查询成员
    /// </summary>
    /// <param name="account">成员账号</param>
    /// <returns>成员信息，如果不存在则返回null</returns>
    public async Task<Member?> GetByAccountAsync(string account)
    {
        await Semaphore.WaitAsync();
        try
        {
            Logger.LogDebug(@"根据账号查询成员: {Account}", account);

            var member = await DbContext.Members
                .Where(m => m.Account == account)
                .FirstAsync();

            Logger.LogDebug(@"查询成员结果: {Found}", member != null ? @"找到" : @"未找到");
            return member;
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, @"根据账号查询成员失败: {Account}", account);
            throw;
        }
        finally
        {
            Semaphore.Release();
        }
    }

    /// <summary>
    /// 检查账号是否已存在
    /// </summary>
    /// <param name="account">要检查的账号</param>
    /// <returns>如果账号已存在返回true，否则返回false</returns>
    public async Task<bool> IsAccountExistsAsync(string account)
    {
        await Semaphore.WaitAsync();
        try
        {
            Logger.LogDebug(@"检查账号是否存在: {Account}", account);

            var exists = await DbContext.Members
                .Where(m => m.Account == account)
                .AnyAsync();

            Logger.LogDebug(@"账号存在性检查结果: {Account} - {Exists}", account, exists);
            return exists;
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, @"检查账号存在性失败: {Account}", account);
            throw;
        }
        finally
        {
            Semaphore.Release();
        }
    }

    /// <summary>
    /// 更新成员余额
    /// </summary>
    /// <param name="account">成员账号</param>
    /// <param name="newBalance">新的余额</param>
    /// <returns>更新是否成功</returns>
    public async Task<bool> UpdateBalanceAsync(string account, decimal newBalance)
    {
        await Semaphore.WaitAsync();
        try
        {
            Logger.LogInformation(@"更新成员余额: {Account}, 新余额: {NewBalance}", account, newBalance);

            var affectedRows = await DbContext.UpdateMember
                .Set(m => m.Balance, newBalance)
                .Set(m => m.UpdatedAt, DateTime.Now)
                .Where(m => m.Account == account)
                .ExecuteAffrowsAsync();

            var success = affectedRows > 0;
            Logger.LogInformation(@"余额更新结果: {Account} - {Success}, 影响行数: {Rows}", account, success, affectedRows);
            return success;
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, @"更新成员余额失败: {Account}", account);
            throw;
        }
        finally
        {
            Semaphore.Release();
        }
    }
    /// <summary>
    /// 根据代理名称查询成员列表
    /// </summary>
    /// <param name="agentName">代理名称</param>
    /// <returns>该代理下的所有成员</returns>
    public async Task<IEnumerable<Member>> GetByAgentNameAsync(string agentName)
    {
        await Semaphore.WaitAsync();
        try
        {
            Logger.LogDebug(@"根据代理名称查询成员: {AgentName}", agentName);

            var members = await DbContext.Members
                .Where(m => m.AgentName == agentName)
                .OrderBy(m => m.CreatedAt)
                .ToListAsync();

            Logger.LogDebug(@"代理成员查询结果: {AgentName} - 找到 {Count} 个成员", agentName, members.Count);
            return members;
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, @"根据代理名称查询成员失败: {AgentName}", agentName);
            throw;
        }
        finally
        {
            Semaphore.Release();
        }
    }

    /// <summary>
    /// 获取余额大于指定金额的成员
    /// </summary>
    /// <param name="minBalance">最小余额</param>
    /// <returns>符合条件的成员列表</returns>
    public async Task<IEnumerable<Member>> GetMembersWithBalanceAboveAsync(decimal minBalance)
    {
        await Semaphore.WaitAsync();
        try
        {
            Logger.LogDebug(@"查询余额大于 {MinBalance} 的成员", minBalance);

            var members = await DbContext.Members
                .Where(m => m.Balance > minBalance)
                .OrderByDescending(m => m.Balance)
                .ToListAsync();

            Logger.LogDebug(@"余额筛选查询结果: 找到 {Count} 个成员", members.Count);
            return members;
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, @"查询高余额成员失败: {MinBalance}", minBalance);
            throw;
        }
        finally
        {
            Semaphore.Release();
        }
    }

    /// <summary>
    /// 批量更新成员返点比例
    /// </summary>
    /// <param name="accounts">成员账号列表</param>
    /// <param name="rebatePercent">新的返点比例</param>
    /// <returns>更新的成员数量</returns>
    public async Task<int> BatchUpdateRebatePercentAsync(IEnumerable<string> accounts, decimal rebatePercent)
    {
        await Semaphore.WaitAsync();
        try
        {
            var accountList = accounts.ToList();
            Logger.LogInformation(@"批量更新返点比例: {Count} 个账号, 新返点: {RebatePercent}%", accountList.Count, rebatePercent);

            var affectedRows = await DbContext.UpdateMember
                .Set(m => m.RebatePercent, rebatePercent)
                .Set(m => m.UpdatedAt, DateTime.Now)
                .Where(m => accountList.Contains(m.Account))
                .ExecuteAffrowsAsync();

            Logger.LogInformation(@"批量更新返点比例完成: 影响 {Rows} 行", affectedRows);
            return affectedRows;
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, @"批量更新返点比例失败");
            throw;
        }
        finally
        {
            Semaphore.Release();
        }
    }

    /// <summary>
    /// 获取成员总数统计
    /// </summary>
    /// <returns>成员总数</returns>
    public async Task<int> GetTotalMemberCountAsync()
    {
        await Semaphore.WaitAsync();
        try
        {
            Logger.LogDebug(@"获取成员总数统计");

            var count = await DbContext.Members.CountAsync();

            Logger.LogDebug(@"成员总数: {Count}", count);
            return count;
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, @"获取成员总数失败");
            throw;
        }
        finally
        {
            Semaphore.Release();
        }
    }

    /// <summary>
    /// 获取成员余额统计信息
    /// </summary>
    /// <returns>包含总余额、平均余额等统计信息</returns>
    public async Task<(decimal TotalBalance, decimal AverageBalance, int MemberCount)> GetBalanceStatisticsAsync()
    {
        await Semaphore.WaitAsync();
        try
        {
            Logger.LogDebug(@"获取成员余额统计信息");

            var statistics = await DbContext.Members
                .GroupBy(m => 1)
                .Select(g => new
                {
                    TotalBalance = g.Sum(m => m.Balance),
                    AverageBalance = g.Average(m => m.Balance),
                    MemberCount = g.Count()
                })
                .FirstAsync();

            var result = (statistics?.TotalBalance ?? 0, statistics?.AverageBalance ?? 0, statistics?.MemberCount ?? 0);
            Logger.LogDebug(@"余额统计结果: 总余额={TotalBalance}, 平均余额={AverageBalance}, 成员数={MemberCount}",
                result.Item1, result.Item2, result.Item3);
            return result;
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, @"获取余额统计信息失败");
            throw;
        }
        finally
        {
            Semaphore.Release();
        }
    }
}