using CommandGuard.Models;
using CommandGuard.Enums;

namespace CommandGuard.Interfaces.Repositories;

/// <summary>
/// 存取款申请仓储接口
/// 提供存取款申请相关的数据访问方法，包括基础CRUD操作和业务特定查询
/// </summary>
public interface IDepositWithdrawRequestRepository : IRepository<DepositWithdrawRequest>
{
    /// <summary>
    /// 根据账号查询存取款申请列表
    /// </summary>
    /// <param name="account">申请人账号</param>
    /// <param name="pageIndex">页码，从1开始</param>
    /// <param name="pageSize">每页数量</param>
    /// <returns>该账号的存取款申请列表</returns>
    Task<(IEnumerable<DepositWithdrawRequest> Requests, int TotalCount)> GetByAccountAsync(string account, int pageIndex = 1, int pageSize = 20);

    /// <summary>
    /// 根据状态查询存取款申请
    /// </summary>
    /// <param name="status">申请状态</param>
    /// <param name="pageIndex">页码，从1开始</param>
    /// <param name="pageSize">每页数量</param>
    /// <returns>指定状态的存取款申请列表</returns>
    Task<(IEnumerable<DepositWithdrawRequest> Requests, int TotalCount)> GetByStatusAsync(EnumDepositWithdrawRequestStatus status, int pageIndex = 1, int pageSize = 20);

    /// <summary>
    /// 根据申请类型查询存取款申请
    /// </summary>
    /// <param name="requestType">申请类型</param>
    /// <param name="pageIndex">页码，从1开始</param>
    /// <param name="pageSize">每页数量</param>
    /// <returns>指定类型的存取款申请列表</returns>
    Task<(IEnumerable<DepositWithdrawRequest> Requests, int TotalCount)> GetByRequestTypeAsync(EnumDepositWithdrawRequestType requestType, int pageIndex = 1, int pageSize = 20);

    /// <summary>
    /// 获取待审核的申请列表
    /// </summary>
    /// <param name="requestType">申请类型，可选</param>
    /// <returns>待审核的申请列表，按创建时间升序排列</returns>
    Task<IEnumerable<DepositWithdrawRequest>> GetPendingRequestsAsync(EnumDepositWithdrawRequestType? requestType = null);

    /// <summary>
    /// 根据时间范围查询存取款申请
    /// </summary>
    /// <param name="startTime">开始时间</param>
    /// <param name="endTime">结束时间</param>
    /// <param name="account">申请人账号，可选</param>
    /// <returns>指定时间范围内的存取款申请</returns>
    Task<IEnumerable<DepositWithdrawRequest>> GetByTimeRangeAsync(DateTime startTime, DateTime endTime, string? account = null);

    /// <summary>
    /// 更新申请状态
    /// </summary>
    /// <param name="requestId">申请ID</param>
    /// <param name="newStatus">新状态</param>
    /// <returns>更新是否成功</returns>
    Task<bool> UpdateStatusAsync(long requestId, EnumDepositWithdrawRequestStatus newStatus);

    /// <summary>
    /// 批量更新申请状态
    /// </summary>
    /// <param name="requestIds">申请ID列表</param>
    /// <param name="newStatus">新状态</param>
    /// <returns>更新的申请数量</returns>
    Task<int> BatchUpdateStatusAsync(IEnumerable<long> requestIds, EnumDepositWithdrawRequestStatus newStatus);

    /// <summary>
    /// 获取存取款统计信息
    /// </summary>
    /// <param name="account">账号，可选</param>
    /// <param name="startTime">开始时间，可选</param>
    /// <param name="endTime">结束时间，可选</param>
    /// <returns>存取款统计信息</returns>
    Task<(int TotalRequests, decimal TotalDepositAmount, decimal TotalWithdrawAmount, int PendingCount)> GetRequestStatisticsAsync(string? account = null, DateTime? startTime = null, DateTime? endTime = null);

    /// <summary>
    /// 根据消息ID查询存取款申请
    /// </summary>
    /// <param name="messageId">关联的消息ID</param>
    /// <returns>关联的存取款申请，如果不存在则返回null</returns>
    Task<DepositWithdrawRequest?> GetByMessageIdAsync(string messageId);
}
