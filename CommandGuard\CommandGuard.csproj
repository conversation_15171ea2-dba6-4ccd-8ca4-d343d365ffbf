﻿<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
        <OutputType>WinExe</OutputType>
        <TargetFramework>net8.0-windows</TargetFramework>
        <Nullable>enable</Nullable>
        <UseWindowsForms>true</UseWindowsForms>
        <ImplicitUsings>enable</ImplicitUsings>
        <ApplicationIcon>Bitdefender.ico</ApplicationIcon>
    </PropertyGroup>

    <ItemGroup>
      <PackageReference Include="Costura.Fody" Version="6.0.0">
        <PrivateAssets>all</PrivateAssets>
<!--        <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>-->
      </PackageReference>
      <PackageReference Include="FreeSql" Version="3.5.213" />
      <PackageReference Include="FreeSql.Provider.Sqlite" Version="3.5.213" />
      <PackageReference Include="Microsoft.Extensions.Configuration" Version="9.0.8" />
      <PackageReference Include="Microsoft.Extensions.Configuration.Json" Version="9.0.8" />
      <PackageReference Include="Microsoft.Extensions.DependencyInjection" Version="9.0.8" />
      <PackageReference Include="Microsoft.Extensions.Logging" Version="9.0.8" />
      <PackageReference Include="Microsoft.Extensions.Options.ConfigurationExtensions" Version="9.0.8" />
      <PackageReference Include="Serilog" Version="4.3.0" />
      <PackageReference Include="Serilog.Extensions.Logging" Version="9.0.2" />
      <PackageReference Include="Serilog.Settings.Configuration" Version="9.0.0" />
      <PackageReference Include="Serilog.Sinks.File" Version="7.0.0" />
    </ItemGroup>

</Project>