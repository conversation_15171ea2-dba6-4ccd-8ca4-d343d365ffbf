﻿namespace CommandGuard.Enums;

/// <summary>
/// 投注订单状态枚举
/// 定义投注订单在整个生命周期中的各种状态
/// </summary>
public enum EnumBetOrderStatus
{
    /// <summary>
    /// 已确认 - 订单已被系统确认，等待开奖
    /// </summary>
    Confirmed = 1,

    /// <summary>
    /// 已取消 - 订单被取消（封盘前取消、系统取消、代理取消等）
    /// </summary>
    Cancelled = 2,

    /// <summary>
    /// 中奖 - 投注中奖，用户获得奖金
    /// </summary>
    Win = 3,

    /// <summary>
    /// 未中奖 - 投注未中奖，用户失去投注金额
    /// </summary>
    Lose = 4,

    /// <summary>
    /// 和局 - 投注结果为和局，退还投注金额
    /// 和局的注单不计入有效投注金额
    /// </summary>
    Draw = 5
}