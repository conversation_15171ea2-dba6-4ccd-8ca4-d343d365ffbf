using CommandGuard.Models;

namespace CommandGuard.Interfaces.Repositories;

/// <summary>
/// 成员仓储接口
/// 提供成员相关的数据访问方法，包括基础CRUD操作和业务特定查询
/// </summary>
public interface IMemberRepository : IRepository<Member>
{
    /// <summary>
    /// 根据账号查询成员
    /// </summary>
    /// <param name="account">成员账号</param>
    /// <returns>成员信息，如果不存在则返回null</returns>
    Task<Member?> GetByAccountAsync(string account);

    /// <summary>
    /// 检查账号是否已存在
    /// </summary>
    /// <param name="account">要检查的账号</param>
    /// <returns>如果账号已存在返回true，否则返回false</returns>
    Task<bool> IsAccountExistsAsync(string account);

    /// <summary>
    /// 更新成员余额
    /// </summary>
    /// <param name="account">成员账号</param>
    /// <param name="newBalance">新的余额</param>
    /// <returns>更新是否成功</returns>
    Task<bool> UpdateBalanceAsync(string account, decimal newBalance);

    /// <summary>
    /// 根据代理名称查询成员列表
    /// </summary>
    /// <param name="agentName">代理名称</param>
    /// <returns>该代理下的所有成员</returns>
    Task<IEnumerable<Member>> GetByAgentNameAsync(string agentName);

    /// <summary>
    /// 获取余额大于指定金额的成员
    /// </summary>
    /// <param name="minBalance">最小余额</param>
    /// <returns>符合条件的成员列表</returns>
    Task<IEnumerable<Member>> GetMembersWithBalanceAboveAsync(decimal minBalance);

    /// <summary>
    /// 批量更新成员返点比例
    /// </summary>
    /// <param name="accounts">成员账号列表</param>
    /// <param name="rebatePercent">新的返点比例</param>
    /// <returns>更新的成员数量</returns>
    Task<int> BatchUpdateRebatePercentAsync(IEnumerable<string> accounts, decimal rebatePercent);

    /// <summary>
    /// 获取成员总数统计
    /// </summary>
    /// <returns>成员总数</returns>
    Task<int> GetTotalMemberCountAsync();

    /// <summary>
    /// 获取成员余额统计信息
    /// </summary>
    /// <returns>包含总余额、平均余额等统计信息</returns>
    Task<(decimal TotalBalance, decimal AverageBalance, int MemberCount)> GetBalanceStatisticsAsync();
}
