﻿using System.ComponentModel.DataAnnotations;
using FreeSql.DataAnnotations;

namespace CommandGuard.Models;

[Table(Name = "Members")]
public class Member
{
    /// <summary>
    /// 成员唯一标识符
    /// 数据库主键，自动递增，用于唯一标识每个成员记录
    /// 数据类型：long - 64位整数，支持海量记录
    /// </summary>
    [Column(IsIdentity = true, IsPrimary = true)]
    public long Id { get; set; }

    /// <summary>
    /// 成员账号
    /// 用于成员登录和身份识别的唯一账号
    /// 约束：最大长度100字符，不能为空，系统内必须唯一
    /// 验证：通过业务层保证唯一性
    /// </summary>
    [Required(ErrorMessage = @"成员账号不能为空")]
    [StringLength(100, ErrorMessage = @"成员账号长度不能超过100个字符")]
    [Column(StringLength = 100, IsNullable = false)]
    public string Account { get; set; } = string.Empty;

    /// <summary>
    /// 成员昵称
    /// 用于显示的友好名称，可以包含中文、英文等字符
    /// 约束：最大长度100字符，不能为空
    /// 用途：界面显示、报表展示等
    /// </summary>
    [Required(ErrorMessage = @"成员昵称不能为空")]
    [StringLength(100, ErrorMessage = @"成员昵称长度不能超过100个字符")]
    [Column(StringLength = 100, IsNullable = false)]
    public string NickName { get; set; } = string.Empty;

    /// <summary>
    /// 成员账户余额
    /// 成员当前可用的资金余额，支持充值、消费、提现等操作
    /// 数据类型：decimal(18,2) - 精度18位，小数点后2位
    /// 约束：不能为负数，默认值为0
    /// 范围：0.00 到 ****************.99
    /// </summary>
    [Required(ErrorMessage = @"账户余额不能为空")]
    [Range(0, ****************.99, ErrorMessage = @"账户余额不能为负数")]
    [Column(Precision = 18, Scale = 2, IsNullable = false)]
    public decimal Balance { get; set; }

    /// <summary>
    /// 成员返点百分比
    /// 成员进行交易时可获得的返点比例
    /// 数据类型：decimal(5,2) - 精度5位，小数点后2位
    /// 约束：范围0.00-100.00，默认值1.50
    /// 示例：1.50表示1.5%的返点比例
    /// </summary>
    [Required(ErrorMessage = @"返点百分比不能为空")]
    [Range(0.00, 100.00, ErrorMessage = @"返点百分比必须在0.00-100.00之间")]
    [Column(Precision = 5, Scale = 2, IsNullable = false)]
    public decimal RebatePercent { get; set; } = 1.5m;

    /// <summary>
    /// 代理名称
    /// 推荐该成员加入的代理名称，用于代理层级管理和佣金分配
    /// 约束：最大长度100字符，可为空（支持无代理的直接成员）
    /// 业务：当成员产生收益时，代理可获得相应的代理返点
    /// </summary>
    [StringLength(100, ErrorMessage = @"代理名称长度不能超过100个字符")]
    [Column(StringLength = 100, IsNullable = true)]
    public string AgentName { get; set; } = string.Empty;

    /// <summary>
    /// 代理返点百分比
    /// 当该成员产生收益时，其代理可获得的返点比例
    /// 数据类型：decimal(5,2) - 精度5位，小数点后2位
    /// 约束：范围0.00-100.00，默认值0.50，不能超过成员返点百分比
    /// 示例：0.50表示代理可获得0.5%的返点
    /// </summary>
    [Required(ErrorMessage = @"代理返点百分比不能为空")]
    [Range(0.00, 100.00, ErrorMessage = @"代理返点百分比必须在0.00-100.00之间")]
    [Column(Precision = 5, Scale = 2, IsNullable = false)]
    public decimal AgentRebatePercent { get; set; } = 0.5m;

    /// <summary>
    /// 记录创建时间
    /// 成员账户首次创建的时间戳，用于审计追踪和数据分析
    /// 约束：不可为空，自动设置为当前时间
    /// 用途：审计日志、统计分析、数据归档等
    /// </summary>
    [Required(ErrorMessage = @"创建时间不能为空")]
    [Column(IsNullable = false, ServerTime = DateTimeKind.Local)]
    public DateTime CreatedAt { get; set; } = DateTime.Now;

    /// <summary>
    /// 记录最后更新时间
    /// 成员信息最后一次修改的时间戳，每次更新记录时自动更新
    /// 约束：不可为空，创建时设置为当前时间，更新时自动刷新
    /// 用途：变更追踪、并发控制、数据同步等
    /// </summary>
    [Required(ErrorMessage = @"更新时间不能为空")]
    [Column(IsNullable = false, ServerTime = DateTimeKind.Local)]
    public DateTime UpdatedAt { get; set; } = DateTime.Now;
}