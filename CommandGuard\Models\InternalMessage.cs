﻿using System.ComponentModel.DataAnnotations;
using FreeSql.DataAnnotations;

namespace CommandGuard.Models;

[Table(Name = "InternalMessages")]
public class InternalMessage
{
    /// <summary>
    /// 消息唯一标识符
    /// 数据库主键，自动递增，用于唯一标识每条消息记录
    /// 数据类型：long - 64位整数，支持海量消息记录
    /// </summary>
    [Column(IsIdentity = true, IsPrimary = true)]
    public long Id { get; set; }

    /// <summary>
    /// 关联发送者账号
    /// 指定消息的发送者账号，用于标识消息的来源和发送者身份
    /// 约束：最大长度100字符，不能为空，关联有效的成员账号或系统账号
    /// 用途：发送者识别、消息溯源、权限验证、消息分组等
    /// 业务：可以是系统预定义账号（如"SYSTEM"、"NOTIFICATION"）或具体的成员账号
    /// 关联：与Member表的Account字段关联，或为系统预定义账号
    /// </summary>
    [Required(ErrorMessage = @"发送者账号不能为空")]
    [StringLength(100, ErrorMessage = @"发送者账号长度不能超过100个字符")]
    [Column(StringLength = 100, IsNullable = false)]
    public string Account { get; set; } = string.Empty;

    /// <summary>
    /// 消息内容
    /// 存储消息的具体文本内容，支持大文本存储
    /// 数据类型：TEXT - 支持大容量文本内容（StringLength = -1表示无长度限制）
    /// 约束：不能为空，支持多行文本、特殊字符、表情符号等
    /// 用途：通知内容、系统消息、用户消息等
    /// </summary>
    [Required(ErrorMessage = @"消息内容不能为空")]
    [Column(StringLength = -1, IsNullable = false)]
    public string Content { get; set; } = string.Empty;

    /// <summary>
    /// 消息已读状态
    /// 标识消息是否已被接收者阅读
    /// 数据类型：bool - true表示已读，false表示未读
    /// 约束：不能为空，默认值为false（未读状态）
    /// 用途：消息状态管理、未读消息统计、消息提醒等
    /// </summary>
    [Required(ErrorMessage = @"消息已读状态不能为空")]
    [Column(IsNullable = false)]
    public bool IsRead { get; set; }

    /// <summary>
    /// 消息创建时间
    /// 消息首次创建的时间戳，用于消息排序和审计追踪
    /// 约束：不可为空，自动设置为当前时间
    /// 用途：消息排序、时间筛选、数据分析、审计日志等
    /// </summary>
    [Required(ErrorMessage = @"创建时间不能为空")]
    [Column(IsNullable = false, ServerTime = DateTimeKind.Local)]
    public DateTime CreatedAt { get; set; } = DateTime.Now;

    /// <summary>
    /// 消息最后更新时间
    /// 消息状态最后一次修改的时间戳（如标记为已读）
    /// 约束：不可为空，创建时设置为当前时间，状态更新时自动刷新
    /// 用途：状态变更追踪、并发控制、数据同步等
    /// </summary>
    [Required(ErrorMessage = @"更新时间不能为空")]
    [Column(IsNullable = false, ServerTime = DateTimeKind.Local)]
    public DateTime UpdatedAt { get; set; } = DateTime.Now;
}