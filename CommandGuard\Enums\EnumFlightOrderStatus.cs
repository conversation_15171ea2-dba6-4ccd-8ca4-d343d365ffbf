namespace CommandGuard.Enums;

/// <summary>
/// 飞单状态枚举
/// 定义投注数据抛售到平台的状态
/// </summary>
public enum EnumFlightOrderStatus
{
    /// <summary>
    /// 待飞单 - 投注数据等待抛售到平台
    /// </summary>
    Pending = 0,

    /// <summary>
    /// 飞单中 - 投注数据正在抛售到平台
    /// </summary>
    Processing = 1,

    /// <summary>
    /// 飞单成功 - 投注数据成功抛售到平台
    /// </summary>
    Success = 2,

    /// <summary>
    /// 飞单失败 - 投注数据抛售到平台失败
    /// </summary>
    Failed = 3
}