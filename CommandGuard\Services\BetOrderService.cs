using CommandGuard.Interfaces.Repositories;
using CommandGuard.Interfaces.Services;
using Microsoft.Extensions.Logging;

namespace CommandGuard.Services;

public class BetOrderService(
    ILogger<BetOrderService> logger,
    IBetOrderRepository betOrderRepository,
    IMemberRepository memberRepository,
    IMemberService memberService,
    IFinancialService financialService
) : IBetOrderService
{

}