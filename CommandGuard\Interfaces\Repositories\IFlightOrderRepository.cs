using CommandGuard.Models;
using CommandGuard.Enums;

namespace CommandGuard.Interfaces.Repositories;

/// <summary>
/// 飞单订单仓储接口
/// 提供飞单订单相关的数据访问方法，包括基础CRUD操作和业务特定查询
/// </summary>
public interface IFlightOrderRepository : IRepository<FlightOrder>
{
    /// <summary>
    /// 根据状态查询飞单订单
    /// </summary>
    /// <param name="status">飞单状态</param>
    /// <param name="pageIndex">页码，从1开始</param>
    /// <param name="pageSize">每页数量</param>
    /// <returns>指定状态的飞单订单列表</returns>
    Task<(IEnumerable<FlightOrder> Orders, int TotalCount)> GetByStatusAsync(EnumFlightOrderStatus status, int pageIndex = 1, int pageSize = 20);

    /// <summary>
    /// 根据彩种查询飞单订单
    /// </summary>
    /// <param name="betLottery">投注彩种</param>
    /// <param name="pageIndex">页码，从1开始</param>
    /// <param name="pageSize">每页数量</param>
    /// <returns>指定彩种的飞单订单列表</returns>
    Task<(IEnumerable<FlightOrder> Orders, int TotalCount)> GetByLotteryAsync(EnumBetLottery betLottery, int pageIndex = 1, int pageSize = 20);

    /// <summary>
    /// 根据期号查询飞单订单
    /// </summary>
    /// <param name="issue">投注期号</param>
    /// <returns>该期号的所有飞单订单</returns>
    Task<IEnumerable<FlightOrder>> GetByIssueAsync(string issue);

    /// <summary>
    /// 根据玩法项目查询飞单订单
    /// </summary>
    /// <param name="playItem">投注玩法项目</param>
    /// <param name="pageIndex">页码，从1开始</param>
    /// <param name="pageSize">每页数量</param>
    /// <returns>指定玩法的飞单订单列表</returns>
    Task<(IEnumerable<FlightOrder> Orders, int TotalCount)> GetByPlayItemAsync(string playItem, int pageIndex = 1, int pageSize = 20);

    /// <summary>
    /// 获取待处理的飞单订单
    /// </summary>
    /// <param name="betLottery">彩种，可选</param>
    /// <returns>待处理的飞单订单列表，按创建时间升序排列</returns>
    Task<IEnumerable<FlightOrder>> GetPendingOrdersAsync(EnumBetLottery? betLottery = null);

    /// <summary>
    /// 根据时间范围查询飞单订单
    /// </summary>
    /// <param name="startTime">开始时间</param>
    /// <param name="endTime">结束时间</param>
    /// <param name="status">状态，可选</param>
    /// <returns>指定时间范围内的飞单订单</returns>
    Task<IEnumerable<FlightOrder>> GetByTimeRangeAsync(DateTime startTime, DateTime endTime, EnumFlightOrderStatus? status = null);

    /// <summary>
    /// 更新飞单状态
    /// </summary>
    /// <param name="orderId">飞单ID</param>
    /// <param name="newStatus">新状态</param>
    /// <returns>更新是否成功</returns>
    Task<bool> UpdateStatusAsync(long orderId, EnumFlightOrderStatus newStatus);

    /// <summary>
    /// 批量更新飞单状态
    /// </summary>
    /// <param name="orderIds">飞单ID列表</param>
    /// <param name="newStatus">新状态</param>
    /// <returns>更新的飞单数量</returns>
    Task<int> BatchUpdateStatusAsync(IEnumerable<long> orderIds, EnumFlightOrderStatus newStatus);

    /// <summary>
    /// 获取飞单统计信息
    /// </summary>
    /// <param name="betLottery">彩种，可选</param>
    /// <param name="startTime">开始时间，可选</param>
    /// <param name="endTime">结束时间，可选</param>
    /// <returns>飞单统计信息</returns>
    Task<(int TotalOrders, decimal TotalAmount, int SuccessCount, int FailedCount)> GetFlightOrderStatisticsAsync(EnumBetLottery? betLottery = null, DateTime? startTime = null, DateTime? endTime = null);

    /// <summary>
    /// 获取各状态飞单的数量统计
    /// </summary>
    /// <param name="betLottery">彩种，可选</param>
    /// <returns>按状态分组的飞单数量</returns>
    Task<Dictionary<EnumFlightOrderStatus, int>> GetStatusCountStatisticsAsync(EnumBetLottery? betLottery = null);

    /// <summary>
    /// 获取需要重试的失败飞单
    /// </summary>
    /// <param name="maxRetryCount">最大重试次数</param>
    /// <returns>需要重试的飞单列表</returns>
    Task<IEnumerable<FlightOrder>> GetRetryableFailedOrdersAsync(int maxRetryCount = 3);
}
