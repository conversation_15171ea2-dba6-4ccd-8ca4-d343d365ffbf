using CommandGuard.Configuration;
using CommandGuard.Interfaces.Services;
using Microsoft.Extensions.Logging;
using System.Net;
using System.Text;
using System.Text.Json;

namespace CommandGuard.Services;

/// <summary>
/// 基于HttpListener的轻量级HTTP API服务实现
/// 提供简洁高效的HTTP API服务，专为CommandGuard项目优化
/// 支持消息创建、状态查询和健康检查功能
/// </summary>
public class HttpApiService : IHttpApiService
{
    #region 私有字段

    private readonly ILogger<HttpApiService> _logger;
    private readonly IMessageService _messageService;
    private readonly HttpListener _httpListener;
    private Task? _listenerTask;
    private bool _disposed;

    // 统计信息
    private static int _totalRequests;
    private static DateTime? _lastRequestTime;
    private static readonly DateTime StartTime = DateTime.Now;

    #endregion

    #region 构造函数

    /// <summary>
    /// 初始化HTTP API服务
    /// </summary>
    /// <param name="logger">日志记录器</param>
    /// <param name="messageService">消息服务</param>
    public HttpApiService(
        ILogger<HttpApiService> logger,
        IMessageService messageService)
    {
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        _messageService = messageService ?? throw new ArgumentNullException(nameof(messageService));
        _httpListener = new HttpListener();
    }

    #endregion

    #region 公共方法

    /// <summary>
    /// 启动HTTP API服务
    /// </summary>
    /// <param name="port">监听端口，默认5000</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>启动任务</returns>
    public async Task StartAsync(int port = 5000, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation(@"正在启动HTTP API服务，端口: {Port}", port);

            // 配置监听地址 (使用+监听所有IP地址)
            _httpListener.Prefixes.Add($"http://+:{port}/");

            // 启动监听器
            _httpListener.Start();

            // 使用RuntimeConfiguration中的统一取消令牌
            var robotCancellationToken = RuntimeConfiguration.RobotServiceCancellation?.Token ?? CancellationToken.None;

            // 开始异步处理请求
            _listenerTask = ProcessRequestsAsync(robotCancellationToken);

            _logger.LogInformation(@"HTTP API服务启动成功，监听地址: http://+:{Port}/", port);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, @"启动HTTP API服务时发生错误");
            await Task.Delay(0, cancellationToken);
            throw;
        }
    }

    /// <summary>
    /// 停止HTTP API服务
    /// </summary>
    /// <returns>停止任务</returns>
    public async Task StopAsync()
    {
        try
        {
            _logger.LogInformation(@"正在停止HTTP API服务");

            // 使用RuntimeConfiguration中的统一取消令牌进行取消
            RuntimeConfiguration.RobotServiceCancellation?.Cancel();

            if (_listenerTask != null)
            {
                await _listenerTask;
            }

            _httpListener.Stop();

            _logger.LogInformation(@"HTTP API服务已停止");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, @"停止HTTP API服务时发生错误");
        }
    }

    #endregion

    #region 私有方法 - 请求处理

    /// <summary>
    /// 处理HTTP请求的主循环
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>处理任务</returns>
    private async Task ProcessRequestsAsync(CancellationToken cancellationToken)
    {
        while (!cancellationToken.IsCancellationRequested && _httpListener.IsListening)
        {
            try
            {
                // 等待请求
                var context = await _httpListener.GetContextAsync();

                // 异步处理请求，不阻塞主循环
                _ = Task.Run(() => HandleRequestAsync(context), cancellationToken);
            }
            catch (ObjectDisposedException)
            {
                // HttpListener已被释放，正常退出
                break;
            }
            catch (HttpListenerException ex) when (ex.ErrorCode == 995)
            {
                // 操作被取消，正常退出
                break;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, @"处理HTTP请求时发生错误");
            }
        }
    }

    /// <summary>
    /// 处理单个HTTP请求
    /// </summary>
    /// <param name="context">HTTP请求上下文</param>
    /// <returns>处理任务</returns>
    private async Task HandleRequestAsync(HttpListenerContext context)
    {
        var request = context.Request;
        var response = context.Response;

        // 更新统计信息
        UpdateRequestStatistics();

        try
        {
            _logger.LogDebug(@"收到HTTP请求: {Method} {Url} 来自 {RemoteEndPoint}",
                request.HttpMethod, request.Url, request.RemoteEndPoint);

            // 设置CORS头
            response.Headers.Add("Access-Control-Allow-Origin", "*");
            response.Headers.Add("Access-Control-Allow-Methods", "GET, POST, OPTIONS");
            response.Headers.Add("Access-Control-Allow-Headers", "Content-Type");

            // 处理OPTIONS预检请求
            if (request.HttpMethod == "OPTIONS")
            {
                response.StatusCode = 200;
                response.Close();
                return;
            }

            // 路由处理
            var path = request.Url?.AbsolutePath.ToLower() ?? "";

            switch (request.HttpMethod.ToUpper())
            {
                case "POST" when path == "/api" || path == "/api/":
                    // 判断游戏服务是否已启动
                    if (!RuntimeConfiguration.IsGameServiceStarted)
                    {
                        await HandleErrorAsync(response, 503, @"游戏服务尚未启动");
                        return;
                    }

                    await HandleCreateMessageAsync(request, response);
                    break;

                case "GET" when path == "/status":
                    await HandleGetStatusAsync(response);
                    break;

                case "GET" when path == "/health":
                    await HandleHealthCheckAsync(response);
                    break;

                default:
                    await HandleNotFoundAsync(response);
                    break;
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, @"处理HTTP请求时发生错误");
            await HandleErrorAsync(response, 500, @"服务器内部错误");
        }
    }

    #endregion

    #region 私有方法 - 端点处理

    /// <summary>
    /// 处理创建消息请求
    /// </summary>
    /// <param name="request">HTTP请求</param>
    /// <param name="response">HTTP响应</param>
    /// <returns>处理任务</returns>
    private async Task HandleCreateMessageAsync(HttpListenerRequest request, HttpListenerResponse response)
    {
        try
        {
            // 读取请求体
            using var reader = new StreamReader(request.InputStream, request.ContentEncoding);
            var requestBody = await reader.ReadToEndAsync();

            _logger.LogInformation(@"收到创建消息请求，数据: {RequestBody}", requestBody);

            // 解析JSON
            var jsonDocument = JsonDocument.Parse(requestBody);
            var requestData = jsonDocument.RootElement;

            // 提取消息数据
            var content = ExtractContent(requestData);
            var sender = ExtractSender(requestData);

            if (string.IsNullOrWhiteSpace(content))
            {
                await HandleErrorAsync(response, 400, @"无效的请求数据格式");
                return;
            }

            // 创建消息
            var message = new Models.InternalMessage
            {
                Account = @"HTTP_API",
                Content = sender == @"UNKNOWN" ? content : $@"[来自: {sender}] {content}",
                IsRead = false,
                CreatedAt = DateTime.Now,
                UpdatedAt = DateTime.Now
            };

            // 保存消息
            var createdMessage = await _messageService.SaveMessageAsync(message);
            _logger.LogInformation(@"消息创建成功，ID: {MessageId}", createdMessage.Id);
            var responseData = new
            {
                success = true,
                message = @"消息创建成功",
                messageId = createdMessage.Id,
                timestamp = DateTime.Now
            };

            await WriteJsonResponseAsync(response, 200, responseData);
        }
        catch (JsonException)
        {
            await HandleErrorAsync(response, 400, @"无效的JSON格式");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, @"处理创建消息请求时发生错误");
            await HandleErrorAsync(response, 500, @"服务器内部错误");
        }
    }

    /// <summary>
    /// 处理状态查询请求
    /// </summary>
    /// <param name="response">HTTP响应</param>
    /// <returns>处理任务</returns>
    private async Task HandleGetStatusAsync(HttpListenerResponse response)
    {
        var uptime = DateTime.Now - StartTime;

        var statusData = new
        {
            status = @"运行中",
            timestamp = DateTime.Now,
            version = @"1.0.0",
            server = @"HttpListener",
            uptime = new
            {
                days = uptime.Days,
                hours = uptime.Hours,
                minutes = uptime.Minutes,
                seconds = uptime.Seconds
            },
            statistics = new
            {
                totalRequests = _totalRequests,
                lastRequestTime = _lastRequestTime
            }
        };

        await WriteJsonResponseAsync(response, 200, statusData);
    }

    /// <summary>
    /// 处理健康检查请求
    /// </summary>
    /// <param name="response">HTTP响应</param>
    /// <returns>处理任务</returns>
    private async Task HandleHealthCheckAsync(HttpListenerResponse response)
    {
        response.StatusCode = 200;
        response.ContentType = "text/plain; charset=utf-8";

        var buffer = "Healthy"u8.ToArray();
        await response.OutputStream.WriteAsync(buffer, 0, buffer.Length);
        response.Close();
    }

    /// <summary>
    /// 处理404请求
    /// </summary>
    /// <param name="response">HTTP响应</param>
    /// <returns>处理任务</returns>
    private async Task HandleNotFoundAsync(HttpListenerResponse response)
    {
        await HandleErrorAsync(response, 404, @"请求的资源不存在");
    }

    #endregion

    #region 私有方法 - 辅助功能

    /// <summary>
    /// 处理错误响应
    /// </summary>
    /// <param name="response">HTTP响应</param>
    /// <param name="statusCode">状态码</param>
    /// <param name="message">错误消息</param>
    /// <returns>处理任务</returns>
    private async Task HandleErrorAsync(HttpListenerResponse response, int statusCode, string message)
    {
        var errorData = new
        {
            success = false,
            message,
            timestamp = DateTime.Now
        };

        await WriteJsonResponseAsync(response, statusCode, errorData);
    }

    /// <summary>
    /// 写入JSON响应
    /// </summary>
    /// <param name="response">HTTP响应</param>
    /// <param name="statusCode">状态码</param>
    /// <param name="data">响应数据</param>
    /// <returns>处理任务</returns>
    private async Task WriteJsonResponseAsync(HttpListenerResponse response, int statusCode, object data)
    {
        response.StatusCode = statusCode;
        response.ContentType = "application/json; charset=utf-8";

        var json = JsonSerializer.Serialize(data, new JsonSerializerOptions
        {
            PropertyNamingPolicy = null,
            WriteIndented = true
        });

        var buffer = Encoding.UTF8.GetBytes(json);
        await response.OutputStream.WriteAsync(buffer, 0, buffer.Length);
        response.Close();
    }

    /// <summary>
    /// 从JSON中提取内容
    /// </summary>
    /// <param name="requestData">请求数据</param>
    /// <returns>提取的内容</returns>
    private string ExtractContent(JsonElement requestData)
    {
        if (requestData.TryGetProperty("content", out var contentElement))
            return contentElement.GetString() ?? string.Empty;

        if (requestData.TryGetProperty("message", out var messageElement))
            return messageElement.GetString() ?? string.Empty;

        if (requestData.TryGetProperty("text", out var textElement))
            return textElement.GetString() ?? string.Empty;

        return requestData.ToString();
    }

    /// <summary>
    /// 从JSON中提取发送者
    /// </summary>
    /// <param name="requestData">请求数据</param>
    /// <returns>发送者名称</returns>
    private string ExtractSender(JsonElement requestData)
    {
        if (requestData.TryGetProperty("account", out var accountElement))
            return accountElement.GetString() ?? @"UNKNOWN";

        if (requestData.TryGetProperty("sender", out var senderElement))
            return senderElement.GetString() ?? @"UNKNOWN";

        return @"UNKNOWN";
    }

    /// <summary>
    /// 更新请求统计信息
    /// </summary>
    private static void UpdateRequestStatistics()
    {
        Interlocked.Increment(ref _totalRequests);
        _lastRequestTime = DateTime.Now;
    }

    #endregion

    #region IDisposable实现

    /// <summary>
    /// 释放资源
    /// </summary>
    public void Dispose()
    {
        if (!_disposed)
        {
            // 使用RuntimeConfiguration中的统一取消令牌进行取消
            RuntimeConfiguration.RobotServiceCancellation?.Cancel();
            _httpListener.Stop();
            _httpListener.Close();
            _disposed = true;
        }

        GC.SuppressFinalize(this);
    }

    #endregion
}